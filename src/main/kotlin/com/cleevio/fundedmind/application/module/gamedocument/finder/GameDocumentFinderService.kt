package com.cleevio.fundedmind.application.module.gamedocument.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.gamedocument.GameDocument
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class GameDocumentFinderService(
    private val gameDocumentRepository: GameDocumentRepository,
) : BaseFinderService<GameDocument>(gameDocumentRepository) {

    override fun errorBlock(message: String) = throw GameDocumentNotFoundException(message)

    override fun getEntityType() = GameDocument::class
}
